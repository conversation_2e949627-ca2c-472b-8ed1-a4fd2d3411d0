import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import { Router } from '@angular/router';
import { Constants, moduleTypes } from 'src/app/config/constants';
import { BookComponent } from '../book/book.component';
declare var $: any;
@Component({
  selector: 'app-list-with-pagination',
  templateUrl: './list-with-pagination.component.html',
  styleUrls: ['./list-with-pagination.component.scss'],
})
export class ListWithPaginationComponent implements OnInit {
  @Input() idKey: string;
  @Input() columns: any[];
  @Input() modalTitle: string;
  @Input() actionPermissions: any;
  @Input() data: any[];
  @Input() viewPageLink: string;
  @Input() onCurrentPageChanged: Function;
  @Input() totalNumberOfRecords: number;
  @Input() onPageSizeChanged: Function;
  @Input() onDeleteRecord: Function;
  @Input() onlockUnlockUser: Function;
  @Input() onUpdateBookRequestStatus: Function;
  @Input() searchTerm: string;
  @Input() module: string;
  @Input() showStaticPagination: boolean;
  @Input() onEditBook: Function;
  @Input() activeTab: string;
  selectedDeleteId: any;
  userData: any;
  columnName: any;
  defProfilePicture = Constants.defaulProfilePicture;
  selectedRequestData: any;
  @Input() perPageItems: number;
  @Input() p: number;
  @ViewChild('addBookModal', { static: false }) addBookModal!: BookComponent;
  moduleTypes = moduleTypes;
  @Input() isSearchApplied: boolean;
  constructor(private router: Router) { }

  ngOnInit(): void { }
  clickedIdex: number = -1;
  selectableColumIndex: number = 0;
  tokenInfo: any = [];
  roleListArray: any[];
  selectedRow: any;
  clickEvent(index: number) {
    this.clickedIdex = index;
    // if (this.onRecordSelected)
    // this.onRecordSelected(this.data[index]);
  }

  handlePageSizeChange(value: number) {
    if (this.showStaticPagination) {
      this.perPageItems = value;
    } else {
      this.onPageSizeChanged(value);
    }
  }

  //   ngOnChanges(changes: SimpleChanges): void {
  //     console.log("this.numberOfRecords",this.totalNumberOfRecords);
  //     if (changes.activeTab && changes.activeTab.currentValue !== changes.activeTab.previousValue) {
  //         this.p = 0;
  //         // this.perPageItems = 10;
  //         // this.handlePageSizeChange(10);
  //     }

  //     if (changes?.totalNumberOfRecords && changes?.totalNumberOfRecords?.currentValue <10) {
  //         this.p = 0;
  //         // this.perPageItems = 10;
  //     }
  // }

  pageChange(event: number) {
    this.p = event;
    if (!this.showStaticPagination) {
      this.onCurrentPageChanged(this.p);
    }
  }

  deleteRecord() {
    this.onDeleteRecord(this.selectedDeleteId);
  }

  openRecord(mode: string, element: any) { }
  logEventAndFetchBooks(event: any): void {
    console.log('Event received in parent:');
    this.onEditBook();
  }
  showMembersList(bookClubId: any) {
    /* const queryParams = {
      bookClubId: bookClubId
    } */
    this.router.navigate([`/admins/clubs/members`], {
      state: { id: bookClubId, view: true, module: this.module },
    });
  }
  // getStarsArray(rating: number) {
  //   const totalStars = 5;
  //   return new Array(totalStars).fill(0).map((_, index) => index < rating);
  // }

  onSwitchClick(event: Event, user: any) {
    event.preventDefault();
    this.userData = user;
    this.showModal('lockUnlockModal');
  }

  updateBookRequestStatus() {
    this.onUpdateBookRequestStatus(this.selectedRequestData);
  }

  lockUnlockUser() {
    this.onlockUnlockUser(this.userData);
  }

  showModal(modalId: string, id?: any) {
    if (id) {
      this.selectedDeleteId = id;
    }

    const modal = document.getElementById(modalId);
    if (modal != null) {
      modal.style.display = 'block';
    }
  }

  hideModal(modalId: string) {
    const modal = document.getElementById(modalId);
    // $(`#${modalId}`).modal('hide');
    if (modal != null) {
      modal.style.display = 'none';
    }
  }

  setSelectedElement(element: any, columnTitle: any) {
    console.log("element", element);

    if (columnTitle == 'Review') {
      this.columnName = 'Review';
      this.showModal('bookReviewModal');
    } else if (columnTitle == 'Requests') {
      this.showModal('requestModal');
    } else if (columnTitle == 'Discussion questions') {
      if (!element.discussionQuestions) {
        element.discussionQuestions = 'No data available';
      }
      this.router.navigate([`/admins/clubs/discussion-questions`], {
        state: element,
      });
    }
    this.selectedRow = element;
  }

  onEditClick(data: any) {
    if (this.module == moduleTypes.BOOK) {
      //this.openModal.emit(data);
      // this.router.navigate([`/admins/book-list/add-book`],{state:{isEdit:true,data:data}});
      this.addBookModal.bookData = data;
      this.addBookModal.isEdit = true;
      this.addBookModal.showModal();
    } else if (this.module == moduleTypes.BOOKREQUESTS) {
      this.selectedRequestData = data;
      this.showModal('updateRequestStatus');
    }
  }

  getStarsArray(rating: number): string[] {
    const totalStars = 5;
    const stars = [];

    for (let i = 0; i < totalStars; i++) {
      if (i < Math.floor(rating)) {
        stars.push('full');
      } else if (i < rating) {
        stars.push('half');
      } else {
        stars.push('empty');
      }
    }
    return stars;
  }

  onRowClick(element: any, columnTitle: any) {
    if (this.module == moduleTypes.BOOK && this.activeTab) {
      this.router.navigate([`/admins/book-list/book-activity`], {
        state: {
          element: element,
          activeTab: this.activeTab,
          searchTerm: this.searchTerm,
        },
      });
    } else {
      return;
    }
  }
  openAddBookModal() {
    this.addBookModal.resetForm();
    this.addBookModal.showModal();
  }

  getImg(img: string) {
    return Constants.s3BaseUrl + img;
  }

  getTextLast(columnTitle: string): boolean {
    return columnTitle === 'Discussion questions' || columnTitle === 'Top shelf' || columnTitle === 'Review';
  }

  shouldShowNoDataMessage(): boolean {
    if (this.module === moduleTypes.BOOK) {
      return (!this.data || this.data.length === 0 && this.isSearchApplied);
    } else {
      return (!this.data || this.data.length === 0);
    }
  }
}
