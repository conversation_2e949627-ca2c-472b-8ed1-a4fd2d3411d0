import { Component, OnInit, ViewChild, HostListener } from '@angular/core';
import { ListWithPaginationComponent } from 'src/app/shared/list-with-pagination/list-with-pagination.component';
import { ToastrService } from 'ngx-toastr';
import { DomSanitizer } from '@angular/platform-browser';
import { Router, ActivatedRoute } from '@angular/router';
import { DataTransferService } from 'src/app/shared/services/data-transfer.service';
import {
  FormGroup,
  FormBuilder,
  Validators,
  FormControl,
} from '@angular/forms';
import { NgxSpinnerService } from 'ngx-spinner';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { getBreadCrumbModules } from 'src/app/config/commonHelper';
import { moduleTypes } from 'src/app/config/constants';
import { BookComponent } from 'src/app/shared/book/book.component';

@Component({
  selector: 'app-book-requests',
  templateUrl: './book-requests.component.html',
  styleUrls: ['./book-requests.component.scss']
})
export class BookRequestsComponent implements OnInit {
  @ViewChild(ListWithPaginationComponent)
  listComponent!: ListWithPaginationComponent;
  @ViewChild('addBookModal') addBookModal!: BookComponent;
  totalNumberOfRecords: any = 0;
  offset: number = 1;
  data: any[] = [];
  columns: any[] = [
    { title: 'Name of the user', dataKey: 'user_name' },
    { title: 'Email', dataKey: 'user_email_id' },
    { title: "Requests", dataKey: "request" },
    // { title: "Lock/Unlock", dataKey: "lock/unlock" },
    // { title: "Action", dataKey: "action" },
  ];
  limit: number = 10;
  menuTitle: string = 'List Of Books';
  searchControl: FormControl = new FormControl();
  searchTerm = '';
  breadCrumbModules = getBreadCrumbModules(moduleTypes.BOOKREQUESTS);
  bookId: any;
  _tomodule: string = moduleTypes.BOOKREQUESTS;
  constructor(
    private dataTransferService: DataTransferService,
    private toastr: ToastrService,
    private ngxSpinnerService: NgxSpinnerService,
    private domSanitizer: DomSanitizer,
    private router: Router,
    private activeRoute: ActivatedRoute,
    private formBuilder: FormBuilder
  ) { }

  ngOnInit(): void {
    this.getBookRequests();
  }

  onCurrentPageChanged = (currentOffset: number) => {
    this.offset = currentOffset;
    this.getBookRequests();
  };

  onPageSizeChanged = (pageSizeChanged: number) => {
    this.offset=1;
    this.limit = pageSizeChanged;
    this.getBookRequests();
  };


  updateRequestStatus = (data: any) => {
    this.ngxSpinnerService.show('globalSpinner');
    this.dataTransferService.updateRequestStatus(data.question_or_feedback_id).subscribe({
      next: (response: any) => {
        if (response.statusCode === 200) {
          this.toastr.success(response.message);
          this.getBookRequests();
          this.hideModalInPagination('updateRequestStatus');
        }
        this.ngxSpinnerService.hide('globalSpinner');
      },
      error: (err) => {
        this.toastr.error(
          err.error.message || 'An error occurred while updating the request status'
        );
        console.log(err);
        this.hideModalInPagination('updateRequestStatus');
        this.ngxSpinnerService.hide('globalSpinner');
      },
      complete: () => {
        this.ngxSpinnerService.hide('globalSpinner');
        this.hideModalInPagination('updateRequestStatus');
      },
    });
  };

  hideModalInPagination(modalId: string) {
    if (this.listComponent) {
      this.listComponent.hideModal(modalId);
    }
  }

  getBookRequests() {
    const payload = {
      search: this.searchTerm,
      limit: this.limit,
      offset: this.offset-1,
      feedbackType: 'book_feedback'
    };
    this.ngxSpinnerService.show('globalSpinner');
    this.dataTransferService.getBookRequests(payload).subscribe({
      next: (value: any) => {
        if (value.data) {
          this.data = value.data;
          this.totalNumberOfRecords = value.count;
          this.ngxSpinnerService.hide('globalSpinner');
        }
      },
      error: (err: any) => {
        this.toastr.error(err.error.message);
        console.log(err);
        this.ngxSpinnerService.hide('globalSpinner');
      },
      complete: () => {
        this.ngxSpinnerService.hide('globalSpinner');
      },
    });
  }
  openAddBookModal() {
    this.addBookModal.showModal();
  }
}
