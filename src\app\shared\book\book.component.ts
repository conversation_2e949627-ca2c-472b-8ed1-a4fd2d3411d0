import { Component, EventEmitter, Input, OnInit, Output, OnChanges, SimpleChanges } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { DataTransferService } from '../services/data-transfer.service';
import { ToastrService } from 'ngx-toastr';
import { NgxSpinnerService } from 'ngx-spinner';

@Component({
  selector: 'app-book',
  templateUrl: './book.component.html',
  styleUrls: ['./book.component.scss']
})
export class BookComponent implements OnInit, OnChanges {
  private _searchedValue: string;

  @Input()
  set searchedValue(value: string) {
    console.log('searchedValue setter called with:', value);
    this._searchedValue = value;
    this._currentSearchedValue = value;
  }

  get searchedValue(): string {
    return this._searchedValue;
  }

  @Output() bookSaved = new EventEmitter<void>();
  @Input() isEdit: boolean = false;
  private _bookData: any;
  private _currentSearchedValue: string;

  @Input()
  set bookData(data: any) {
    this._bookData = data;
    this.initializeForm();
  }

  get bookData(): any {
    return this._bookData;
  }
  bookId: any;
  addNewBookForm: FormGroup;
  constructor(
    private formBuilder: FormBuilder,
    private dataTransferService: DataTransferService,
    private toastr: ToastrService,
    private ngxSpinnerService: NgxSpinnerService,
  ) {

  }

  ngOnInit(): void {
    console.log('searchedValue in init', this.searchedValue);
    this._currentSearchedValue = this.searchedValue;
    this.initializeForm();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['searchedValue']) {
      console.log('searchedValue changed:', changes['searchedValue'].currentValue);
      this._currentSearchedValue = changes['searchedValue'].currentValue;
    }
  }
  initializeForm() {
    console.log('initializeForm', this.bookData)
    this.addNewBookForm = this.formBuilder.group({
      bookName: [this.bookData?.bookName || '', Validators.required],
      bookAuthor: [this.bookData?.bookAuthor || '', Validators.required],
    });
  }
  showModal() {
    console.log('searchedValue in shoModal', this.searchedValue);
    console.log('_currentSearchedValue in shoModal', this._currentSearchedValue);
    const modalElement = document.getElementById('addBookModal');
    if (modalElement) {
      modalElement.classList.add('show');
      modalElement.style.display = 'block';
    }
    // console.log('AFter Open showModal', this.searchedValue);
  }

  hideModal() {
    const modalElement = document.getElementById('addBookModal');
    if (modalElement) {
      modalElement.classList.remove('show');
      modalElement.style.display = 'none';
    }
    this.resetForm();
  }

  onSubmit() {
    console.log('searchedValue before calling api : ', this.searchedValue);
    console.log('_currentSearchedValue before calling api : ', this._currentSearchedValue);
    if (this.addNewBookForm.valid) {
      this.ngxSpinnerService.show('globalSpinner');
      const formData = this.addNewBookForm.value;
      const apiCall = this.isEdit
        ? this.dataTransferService.editBook(formData, this.bookData.bookId)
        : this.dataTransferService.addBook(formData);
      apiCall.subscribe({
        next: (response: any) => {
          this.toastr.success(this.isEdit ? response.message : "Book added successfully");
          //this.hideModal();
          this.ngxSpinnerService.hide('globalSpinner');
          console.log('searchedValue', this.searchedValue);
          console.log('_currentSearchedValue', this._currentSearchedValue);


          if(this._currentSearchedValue || this.searchedValue){
            setTimeout(() => {
              console.log('bookSaved event emitted successfully');
            this.bookSaved.emit();
          }, 1000);
        }
        },
        error: (err) => {
          const errorMessage = err.error.message || 'An error occurred while saving the book';
          this.toastr.error(errorMessage);
          console.error('Error saving book:', err);
          this.ngxSpinnerService.hide('globalSpinner');

        },
        complete: () => {
          this.ngxSpinnerService.hide('globalSpinner');
          this.hideModal()
        },
      });
    }
  }


  editBook() {
    if (this.addNewBookForm.valid) {
      this.ngxSpinnerService.show('globalSpinner');
      let id = this.bookId;
      const payload = {
        bookName:
          this.addNewBookForm.get('bookName')?.value,
        bookAuthor:
          this.addNewBookForm.get('bookAuthor')?.value
      };
      this.dataTransferService.editBook(payload, id).subscribe({
        next: (response: any) => {
          if (response.statusCode === 200) {
            this.toastr.success(response.message);
            console.log(`Book updated successfully with ID: ${id}`);
          }
          this.ngxSpinnerService.hide('globalSpinner');
        },
        error: (err) => {
          this.toastr.error(
            err.error.message || 'An error occurred while editing the book'
          );
          console.log(err);
          this.ngxSpinnerService.hide('globalSpinner');
        },
        complete: () => {
          this.ngxSpinnerService.hide('globalSpinner');
        },
      });
    } else {
      this.toastr.error('Please fill all required fields correctly');
    }
  }

  addBook() {
    console.log('this.addNewBookForm', this.addNewBookForm.value);
    if (this.addNewBookForm.valid) {
      this.ngxSpinnerService.show('globalSpinner');
      this.dataTransferService.addBook(this.addNewBookForm.value).subscribe({
        next: (response: any) => {
          if (response.statusCode === 200) {
            this.toastr.success(response.message);

            const bookId = response.data;
            console.log(`Book added successfully with ID: ${bookId}`);
            // this.getbooksList();
            //this.hideModal('staticBackdrop');
          }

          this.ngxSpinnerService.hide('globalSpinner');
        },
        error: (err) => {
          this.toastr.error(
            err.error.message || 'An error occurred while adding the book'
          );
          console.log(err);

          this.ngxSpinnerService.hide('globalSpinner');
        },
        complete: () => {
          this.ngxSpinnerService.hide('globalSpinner');
        },
      });
    } else {
      this.toastr.error('Please fill all required fields correctly');
    }
  }
  resetForm() {
    this.isEdit = false;
    this.addNewBookForm.reset();
  }
}
